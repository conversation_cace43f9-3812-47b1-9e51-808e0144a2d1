{"fbcsp_svm": {"type": "SVC", "params": {"C": 0.1, "kernel": "linear", "probability": true, "random_state": 42, "class_weight": "balanced"}}, "tef_rf": {"type": "RandomForestClassifier", "params": {"n_estimators": 20, "max_depth": 4, "min_samples_split": 10, "min_samples_leaf": 5, "max_features": 0.5, "bootstrap": true, "oob_score": true, "random_state": 42, "class_weight": "balanced"}}, "riemannian_meanfield": {"type": "MeanField", "params": {"power_list": [-0.5, 0, 0.5], "method_label": "sum_means", "metric": "logeuclid", "n_jobs": 1}, "fallback": {"type": "SVC", "params": {"C": 0.1, "kernel": "rbf", "gamma": "scale", "probability": true, "random_state": 42}}}, "tangent_space_lr": {"type": "LogisticRegression", "params": {"C": 1, "max_iter": 10000, "random_state": 42, "solver": "lbfgs", "class_weight": "balanced", "penalty": "l2"}}, "plv_svm": {"type": "SVC", "params": {"C": 0.1, "kernel": "linear", "gamma": "auto", "probability": true, "random_state": 42, "class_weight": "balanced"}}, "dynamic_weights": {"enabled": true, "window_size": 20, "update_frequency": 10, "temperature": 2.0, "min_weight": 0.05, "performance_decay": 0.95}, "classifier_smoothing": {"enabled": true, "fbcsp_svm": {"enabled": true, "alpha": 0.3}, "riemannian_meanfield": {"enabled": false, "alpha": 0.2}, "tangent_space_lr": {"enabled": false, "alpha": 0.2}, "plv_svm": {"enabled": false, "alpha": 0.1}, "tef_rf": {"enabled": true, "alpha": 0.25}}}